# CinePanda Backend

Express.js server with TypeScript and MongoDB (Mongoose) for the CinePanda project.

## Features

- ✅ Express.js with TypeScript
- ✅ MongoDB with Mongoose ODM
- ✅ CORS configuration
- ✅ Request logging with Morgan
- ✅ Environment variables with dotenv
- ✅ Error handling middleware
- ✅ Health check endpoint
- ✅ Structured project organization
- ✅ Database connection management

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- MongoDB (local installation or MongoDB Atlas)
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Copy environment variables:
```bash
cp .env.example .env
```

3. Update your `.env` file with your MongoDB connection string:
```env
MONGODB_URI=mongodb://localhost:27017/cinepanda
```

4. Start development server:
```bash
npm run dev
```

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run clean` - Clean build directory

## API Endpoints

- `GET /health` - Health check endpoint
- `GET /api` - API root endpoint
- `GET /api/users` - Get all users from database
- `POST /api/users` - Create a new user

## Project Structure

```
src/
├── config/         # Configuration files
│   └── database.ts # MongoDB connection setup
├── controllers/    # Route controllers
├── middleware/     # Custom middleware
├── models/         # Mongoose models
│   └── User.ts     # User model
├── routes/         # Route definitions
├── types/          # TypeScript type definitions
├── app.ts          # Express app configuration
└── index.ts        # Application entry point
```

## Database

The application uses MongoDB with Mongoose ODM. The server will only start after a successful database connection. Make sure MongoDB is running before starting the server.
