{"name": "cinepanda-backend", "version": "1.0.0", "description": "Backend server for CinePanda", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "morgan": "^1.10.0", "dotenv": "^16.3.1", "mongoose": "^8.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/node": "^20.10.0", "typescript": "^5.3.0", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "rimraf": "^5.0.5", "@types/mongoose": "^5.11.97"}}